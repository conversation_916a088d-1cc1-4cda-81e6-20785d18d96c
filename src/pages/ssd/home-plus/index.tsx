/**
 * @file 首支一体页
 */

import { useEffect, useRef, useState } from 'react';
import { Dialog } from 'antd-mobile';
import { log } from '@alife/dtao-iec-spm-log';
import { defineDataLoader, definePageConfig, defineServerDataLoader } from 'ice';
import { includes } from 'lodash-es';

import LinkUtil from '@/utils/link';
import { PAGES } from '@/common/constant';
import BottomTextBar from '@/components/SSD/BottomTextBar';
import { SsdHomePlusSkeleton } from '@/components/SSD/Skeleton';
import Layout from '@/components/Layout/ssd';
import Notice from '@/components/SSD/Notice';
import { checkMayizhixinCreditRouter } from '@/store/credit/model';
import { contractAsync } from '@/store/loan/actions';
import { ssdHomePlusCSR, SsdHomePlusPData, ssdHomePlusSSR } from '@/services/ssd-home-plus';
import { useVisibilityChangeRefresh } from '@/hooks';
import { useFSPData } from '@/hooks/useFSPData';
import { addInitLog } from '@/utils/log';
import { checkPushPage, removePushPage } from '@/utils/session';
import { PROCESS_ACTION } from '@/store/loan';
import { checkHmRejectAlert } from '@/components/HmReject';
import { _, getOrigin, getQueryParams } from '@/utils';
import { homeMayizhixinPageLog } from '@/utils/goc';

import Qualified from './Qualified';
import Closed from './Closed';
import Popup from './Popup';
import styles from './index.module.scss';

export const serverDataLoader = defineServerDataLoader(async () => {
  const data = await ssdHomePlusSSR();
  return data;
});

const MayiZhixinRedirectPageMap = {
  center: PAGES.SsdCenter,
  recordlist: PAGES.RecordList,
  repaybill: PAGES.SsdRepayBill,
  coupon: PAGES.SsdDiscountCoupon,
};

export const dataLoader = defineDataLoader(async () => {
  const data = await ssdHomePlusCSR('PREFETCH');
  return data;
});

export default function SsdHomePlus() {
  const { fspData, doReInit } = useFSPData<SsdHomePlusPData>({
    init: ssdHomePlusCSR,
  });
  const [reloadTag, setReloadTag] = useState(0);
  const [clearTag, setClearTag] = useState(0);
  const processActionRef = useRef<PROCESS_ACTION>();

  const {
    creditApplyConsult,
    loanSchemaInfo,
  } = fspData?.data || {};

  // 同步支用的processAction状态
  const asyncProcessAction = (actionValue: PROCESS_ACTION) => {
    processActionRef.current = actionValue;
  };

  // 重载页面方法
  const doHomePlusReload = async () => {
    if (includes(['AUTENTICATING', 'SUBMITTING'], processActionRef.current)) {
      return;
    }
    setClearTag((pre) => {
      return pre + 1;
    });
    await doReInit();
    setReloadTag((cur) => {
      return cur + 1;
    });
  };

  // push页面返回后，专用重载
  const pushHomePlusReInit = async () => {
    if (includes(['AUTENTICATING', 'SUBMITTING'], processActionRef.current)) {
      return;
    }
    if (checkPushPage()) {
      removePushPage();
      await doReInit();
    }
  };

  const doContractAsync = async () => {
    await contractAsync();
  };

  const doInit = async () => {
    if (!fspData) {
      return;
    }
    addInitLog(fspData);
    if (fspData?.success === false) {
      setTimeout(() => {
        homeMayizhixinPageLog({});
      }, 300);
      Dialog.show({
        content: '系统开小差，请稍后重试',
        closeOnAction: true,
        actions: [[{
          text: '我知道了',
          key: 'confirm',
          bold: true,
          onClick: LinkUtil.reload,
        }]],
      });
      return;
    }

    if (creditApplyConsult && !loanSchemaInfo) {
      setTimeout(() => {
        homeMayizhixinPageLog({
          message: 'ssd-home-plus-uncredit',
        });
        LinkUtil.replacePage('index');
      }, 300);
      return;
    }

    if (!creditApplyConsult || !loanSchemaInfo) {
      return;
    }
    // 先处理退出中的跳转
    if (
      // 退出中
      loanSchemaInfo?.creditClosing === true ||
      // 已退出但授信咨询状态未更新为已退出（防止授信页-首支页循环跳转）
      (loanSchemaInfo?.creditContractStatus === 'CLOSED' && creditApplyConsult?.creditContractStatus !== 'CLOSED')
    ) {
      LinkUtil.replacePage(PAGES.SsdClose, {
        from: 'home',
      });
      return;
    }
    log.setGlobalExtra({
      institution: loanSchemaInfo?.institution,
      loanVersion: loanSchemaInfo?.loanVersion,
      origin: getOrigin(),
    });
    // 未签约/主动退出的用户 回跳签约页
    const routerPage = checkMayizhixinCreditRouter({
      ...creditApplyConsult,
      // 授信合约接口中的合约状态滞后不准确，需使用用户信息中实时查询的合约状态
      creditContractStatus: loanSchemaInfo?.creditContractStatus,
    });
    setTimeout(() => {
      homeMayizhixinPageLog({ routerPage });
    }, 300);
    if (routerPage !== PAGES.SsdHomePlus) {
      log.addOtherLog('ssd-home-plus-to-other-page', {
        routerPage,
      });
      LinkUtil.replacePage('index');
    }
  };

  const renderHome = () => {
    // 使用用户信息中实时查询的合约状态
    switch (loanSchemaInfo?.creditContractStatus) {
      case 'QUALIFIED':
      case 'FROZEN':
        return (
          <Qualified
            pageVersion={loanSchemaInfo?.loanVersion}
            reload={doHomePlusReload}
            reloadTag={reloadTag}
            clearTag={clearTag}
            payload={loanSchemaInfo}
            asyncProcessAction={asyncProcessAction}
          />
        );
      case 'CLEARED':
        return <Closed payload={loanSchemaInfo} />;
      default:
        return <SsdHomePlusSkeleton />;
    }
  };

  useEffect(() => {
    // 处理到了home页去指定页面，就是url上的to参数
    const toParam = _.get(getQueryParams(), 'to');
    const targetPage = _.get(MayiZhixinRedirectPageMap, `${toParam}`);

    doContractAsync();

    if (targetPage) {
      LinkUtil.pushPage(targetPage);
    }
  }, []);

  // 初始化
  useEffect(() => {
    checkHmRejectAlert(PAGES.SsdHomePlus);
    doInit();
  }, [fspData]);

  useVisibilityChangeRefresh(pushHomePlusReInit);

  return (
    <Layout>
      <div className={styles.ssdHome}>
        <div className={styles.main}>
          <div className={styles.slogan}>
            <i className={styles.logo} />
            <p className={styles.title}>随身贷</p>
          </div>
          <Notice />
          {renderHome()}
          <Popup />
        </div>
        <BottomTextBar customClassName={styles.bottom} />
      </div>
    </Layout>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '借钱',
  spm: {
    spmB: PAGES.SsdHomePlus,
  },
}));
