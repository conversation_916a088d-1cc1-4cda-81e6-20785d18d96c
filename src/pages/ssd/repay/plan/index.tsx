import { useState, useEffect } from 'react';
import { definePageConfig } from 'ice';
import SsdEmpty from '@/components/SSD/Empty';
import classnames from 'classnames';
import FullLoading from '@/components/FullLoading';
import styles from './index.module.scss';
import { dayjsFormat, amountFormat, isGreaterThan0, _, dayjsYYMD, isToday } from '@/utils';
import { queryInstallmentBillList } from '@/store/repay/actions';
import { PAGES, REPAYMENT_METHOD } from '@/common/constant';
import LinkUtil from '@/utils/link';
import CommonFixedNavBar from '@/components/CommonFixedNavBar';
import { log } from '@alife/dtao-iec-spm-log';

export default function RepayPlan() {
  const [installmentBillPageData, setInstallmentBillPageData] = useState<any>(null);
  const [activeBill, setActiveBill] = useState<any>(null);

  const handleInstallmentBillItemClick = (billItem) => {
    if (billItem.installmentEndDate === activeBill.installmentEndDate) return;
    setActiveBill(billItem);
  };

  const renderInstallmentTitle = (installmentItem) => {
    if (installmentItem.installmentBillStatus === 'OVERDUE') {
      return '逾期';
    }

    if (isToday(installmentItem.installmentEndDate)) {
      return '今日';
    }

    return dayjsFormat(installmentItem.installmentEndDate, 'MM月DD日');
  };

  useEffect(() => {
    log.addVisitLog(PAGES.RepayPlan);
    queryInstallmentBillList().then((res) => {
      setInstallmentBillPageData(res);
      setActiveBill(_.first(res.installmentBillList));
    }).catch((e) => {
      log.addErrorLog('ssd-repay-plan-query', {
        code: e.message,
      });
    });
  }, []);

  if (!installmentBillPageData) {
    return <FullLoading visible />;
  }

  if (_.isEmpty(installmentBillPageData.installmentBillList)) {
    return <SsdEmpty title="当前无还款计划" />;
  }

  const hasOverDueInstallment = !!_.find(
    installmentBillPageData.installmentBillList,
    (billItem) => billItem.installmentBillStatus === 'OVERDUE',
  );

  return (
    <div className={styles.repayPlan}>
      <CommonFixedNavBar bgColor="#f3f6f8" />
      <div
        className={classnames([
          styles.billListWrap,
          hasOverDueInstallment && styles.billListWrapHasOverDue,
        ])}
      >
        {_.map(installmentBillPageData.installmentBillList, (billItem) => {
          const isOverdue = billItem.installmentBillStatus === 'OVERDUE';
          const isActive = activeBill.installmentEndDate === billItem.installmentEndDate;

          return (
            <div
              key={billItem.installmentEndDate}
              onClick={() => {
                handleInstallmentBillItemClick(billItem);
              }}
              className={classnames([
                styles.billItem,
                isOverdue && styles.billItemOverdue,
                isActive && styles.billItemActive,
                hasOverDueInstallment && styles.billItemHasOverDue,
              ])}
            >
              <div className={styles.billEndDate}>
                {dayjsFormat(billItem.installmentEndDate, 'M月D日')}
              </div>
              <div className={styles.billAmount}>¥{amountFormat(billItem.surplusTotalAmount)}</div>
              {isOverdue && (
                <div className={classnames(['common-tag', isActive ? 'white' : 'red'])}>逾期</div>
              )}
            </div>
          );
        })}
      </div>
      <div className={classnames([styles.activeBillInfo, 'common-card'])}>
        <div>{renderInstallmentTitle(activeBill)}应还 (元)</div>
        <div className={styles.surplusTotalAmount}>{activeBill.surplusTotalAmount}</div>
        <div className={styles.surplusTotalAmountDetail}>
          含本金{amountFormat(activeBill.surplusPrincipal)}元，利息
          {amountFormat(activeBill.surplusInterest)}元
          {isGreaterThan0(activeBill.surplusPenalty) && `，罚息${activeBill.surplusPenalty}元`}
        </div>
        <img src="https://gw.alicdn.com/imgextra/i3/O1CN018qpDke1gLlA6g1BOt_!!6000000004126-2-tps-638-8.png" />
        {activeBill && (
          <div className={styles.loanContractInstallmentList}>
            {_.map(activeBill.installmentDetailList, (item) => {
              return (
                <div key={item.loanOrderId} className={styles.loanContractInstallmentDetailWrap}>
                  <div className={styles.installmentDetailTop}>
                    <span className={styles.installmentDetailDateAndAmount}>
                      {dayjsYYMD(item.loanStartDate)}借{amountFormat(item.loanedAmount)}元
                      <span className="common-divide-line-vertical" />
                      {REPAYMENT_METHOD[item.repaymentMethod]}
                    </span>
                    <a
                      onClick={() => {
                        log.addClickLog('loan-detail');
                        LinkUtil.pushPage(PAGES.LoanDetail, { loanOrderId: item.loanOrderId });
                      }}
                      className={styles.goToLoanDetailBtn}
                    >
                      借款详情
                    </a>
                  </div>
                  <div className="common-divide-line" />
                  <div className={styles.installmentDetailMid}>
                    <span>
                      第{item.installmentNumber}/{item.totalInstallmentCount}期
                    </span>
                    <span className="alibaba-font-md">
                      ¥{amountFormat(item.surplusTotalAmount)}
                    </span>
                  </div>
                  <div className={classnames([styles.installmentDetailBottom, 'common-card-2'])}>
                    <div className="flex-space-between">
                      <div>本金</div>
                      <div>¥{amountFormat(item.surplusPrincipal)}</div>
                    </div>
                    <div className="flex-space-between">
                      <div>利息</div>
                      <div>¥{amountFormat(item.surplusInterest)}</div>
                    </div>
                    {isGreaterThan0(item.surplusPenalty) && (
                      <div className="flex-space-between">
                        <div>罚息</div>
                        <div>¥{amountFormat(item.surplusPenalty)}</div>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
}

export const pageConfig = definePageConfig(() => ({
  title: '还款计划',
  spm: {
    spmB: PAGES.SsdRepayPlan,
  },
}));
