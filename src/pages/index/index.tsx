import { definePageConfig } from 'ice';
import { useEffect, lazy } from 'react';
// import classNames from 'classnames';
import { useCreditRouter } from '@/hooks';
import LinkUtil from '@/utils/link';
import { PAGES } from '@/common/constant';
import { getQueryParams } from '@/utils';
// import { SsdHomePlusSkeleton } from '@/components/SSD/Skeleton';
import { routerPageLog } from '@/utils/goc';
import { setRouterTimeStore } from '@/utils/storage';

const SsdCreditLoanConsistent = lazy(() => import('@/components/SSD/CreditLoanConsistent'));

// import styles from './index.module.scss';

export default function Index() {
  const { creditConsultData, routerPage, inited } = useCreditRouter({
    useOneConfig: true,
  });
  const queryParams = getQueryParams();

  useEffect(() => {
    setRouterTimeStore();
  }, []);

  if (!inited) return;

  routerPageLog({
    routerPage,
    creditConsultData,
  });

  if (!creditConsultData) return;

  if (routerPage === PAGES.CreditFallback) {
    LinkUtil.locationReplace(PAGES.CreditFallback);
    return;
  }
  if (routerPage === PAGES.SsdCreditLoanConsistent) {
    return <SsdCreditLoanConsistent />;
  }

  LinkUtil.locationReplace(routerPage, queryParams);
}

export const pageConfig = definePageConfig(() => ({
  title: '借钱',
  spm: {
    spmB: PAGES.Index,
  },
}));
