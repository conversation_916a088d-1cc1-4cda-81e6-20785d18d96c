import React from 'react';
import styles from './index.module.scss';
import { definePageConfig } from 'ice';

const FigmaPage: React.FC = () => {
  return (
    <div className={styles['figma-page']}>
      <div className={styles['design-your-own-card']}>
        <p className={styles['card-title']}>Design your own card here!</p>
        <div className={styles['vector-132']}></div>
        <div className={styles['unsplash-image']}></div>
        <p className={styles['image-tip']} style={{ opacity: 0.5 }}>Use this image to complete your card</p>
        <div className={styles['thumbnail']}>
          {/* 此处添加 Thumbnail 相关内容 */}
          <div className={styles['illo-group']}>
            <div className={styles['vector-124']}></div>
            <div className={styles['vector-125']}></div>
            <div className={styles['ellipse-73']}></div>
            <div className={styles['ellipse-74']}></div>
            <div className={styles['ellipse-75']}></div>
          </div>
          <div className={styles['illo-group']}>
            <div className={styles['vector-126']}></div>
            <div className={styles['vector-127']}></div>
            <div className={styles['ellipse-66']}></div>
            <div className={styles['ellipse-67']}></div>
            <div className={styles['ellipse-68']}></div>
            <div className={styles['ellipse-69']}></div>
            <div className={styles['ellipse-70']}></div>
            <div className={styles['ellipse-71']}></div>
            <div className={styles['ellipse-72']}></div>
          </div>
          <div className={styles['cursor-labels']}>
            <div className={styles['cursor']}>
              <div className={styles['cursor-image']}></div>
            </div>
          </div>
          <div className={styles['cursor-labels']}>
            <div className={styles['cursor']}>
              <div className={styles['cursor-image']}></div>
            </div>
          </div>
          <div className={styles['shadow']}></div>
          <div className={styles['bounding-headline']}>
            <div className={styles['corner']}></div>
            <div className={styles['corner']}></div>
            <div className={styles['corner']}></div>
            <div className={styles['corner']}></div>
            <p className={styles['headline-text']}>Figma basics</p>
          </div>
          <div className={styles['cursor-labels-instance']}>
            <div className={styles['cursor-instance']}>
              <div className={styles['cursor-image-instance']}></div>
            </div>
          </div>
        </div>
        <div className={styles['about-this-file']}>
          <div className={styles['ellipse-71']}></div>
          <div className={styles['ellipse-72']}></div>
          <p className={styles['file-title']}>Meet Figma, in 3 minutes</p>
          <p className={styles['file-description']}>This space is for learning the basics of Figma, and to practice on some sample designs. </p>
        </div>
        <div className={styles['whats-in-this']}>
          <p className={styles['instruction-text']}>Check out the examples below. They're for you to hack up, replicate, and make your own.</p>
          <div className={styles['image-1']}></div>
          <div className={styles['image-2']}></div>
          <div className={styles['image-3']}></div>
          <div className={styles['vector-133']}></div>
        </div>
        <div className={styles['practice-designs']}>
          <div className={styles['shadow']}></div>
          <div className={styles['bounding-headline']}>
            <div className={styles['corner']}></div>
            <div className={styles['corner']}></div>
            <div className={styles['corner']}></div>
            <div className={styles['corner']}></div>
            <p className={styles['designs-title']}>Practice designs</p>
          </div>
          <div className={styles['vector-group']}>
            <div className={styles['ellipse-278']}></div>
            <div className={styles['ellipse-277']}></div>
            <div className={styles['ellipse-276']}></div>
          </div>
          <div className={styles['common-icon-pen']}>
            <div className={styles['union']}>
              <div className={styles['ellipse-6']}></div>
              <div className={styles['rectangle-4']}></div>
              <div className={styles['vector-16']}></div>
            </div>
            <div className={styles['union']}>
              <div className={styles['union']}>
                <div className={styles['ellipse-6']}></div>
                <div className={styles['rectangle-4']}></div>
                <div className={styles['vector-16']}></div>
              </div>
              <div className={styles['vector-17']}></div>
              <div className={styles['ellipse-7']}></div>
            </div>
          </div>
          <div className={styles['cursor-labels-instance']}>
            <div className={styles['cursor-instance']}>
              <div className={styles['cursor-image-instance']}></div>
            </div>
          </div>
        </div>
        <div className={styles['homepage']}>
          <p className={styles['homepage-text']}>We're farmers, purveyors, and eaters of organically grown food.</p>
          <div className={styles['button']}>
            <p className={styles['button-text']}>Browse our shop</p>
          </div>
          {/* 此处截断的文本内容未展示，可按需补充 */}
        </div>
      </div>
    </div>
  );
};

export default FigmaPage;

export const pageConfig = definePageConfig(() => ({
  title: 'Figma练习',
  spm: {
    spmB: 'figmapage',
  },
}));