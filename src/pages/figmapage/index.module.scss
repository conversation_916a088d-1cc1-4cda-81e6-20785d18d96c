.figma-page {
  width: 100%;
  height: 100%;
  padding: 20px;
  box-sizing: border-box;

  .design-your-own-card {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .card-title {
      font-family: style_SOLBAR;
      color: fill_AIM7A4;
      layout: layout_07GVT3;
    }

    .vector-132 {
      strokes: stroke_ZZN9X5;
      layout: layout_4HP9X4;
    }

    .unsplash-image {
      fills: fill_RKUGHX;
      layout: layout_GPGFFW;
    }

    .image-tip {
      font-family: style_SOLBAR;
      color: fill_AIM7A4;
      layout: layout_VFMA2W;
    }

    .thumbnail {
      fills: fill_YOWY92;
      layout: layout_SG6HUZ;

      .illo-group {
        display: flex;
        flex-direction: column;
        gap: 10px;

        .vector-124 {
          strokes: stroke_S50AOJ;
          layout: layout_ALA2UV;
        }

        .vector-125 {
          strokes: stroke_3ZBXST;
          layout: layout_ALA2UV;
        }

        .ellipse-73 {
          fills: fill_YOWY92;
          strokes: stroke_3ZBXST;
          layout: layout_1XB594;
        }

        .ellipse-74 {
          fills: fill_YOWY92;
          strokes: stroke_3ZBXST;
          layout: layout_167RM9;
        }

        .ellipse-75 {
          fills: fill_YOWY92;
          strokes: stroke_3ZBXST;
          layout: layout_SY3190;
        }
      }

      .illo-group:nth-child(2) {
        .vector-126 {
          strokes: stroke_9C9P1U;
          layout: layout_JTK6OY;
        }

        .vector-127 {
          strokes: stroke_3ZBXST;
          layout: layout_JTK6OY;
        }

        .ellipse-66 {
          fills: fill_YOWY92;
          strokes: stroke_3ZBXST;
          layout: layout_1URGHD;
        }

        .ellipse-67 {
          fills: fill_YOWY92;
          strokes: stroke_3ZBXST;
          layout: layout_UU5TEG;
        }

        .ellipse-68 {
          fills: fill_YOWY92;
          strokes: stroke_3ZBXST;
          layout: layout_AMR7UU;
        }

        .ellipse-69 {
          fills: fill_YOWY92;
          strokes: stroke_3ZBXST;
          layout: layout_2VKBTA;
        }

        .ellipse-70 {
          fills: fill_YOWY92;
          strokes: stroke_3ZBXST;
          layout: layout_AOP7I2;
        }

        .ellipse-71 {
          fills: fill_YOWY92;
          strokes: stroke_FG5WB2;
          layout: layout_O3FJGU;
        }

        .ellipse-72 {
          fills: fill_YOWY92;
          strokes: stroke_FG5WB2;
          layout: layout_IY26QG;
        }
      }

      .cursor-labels {
        fills: fill_BUP8OD;
        strokes: stroke_3ZBXST;
        layout: layout_9SHFFK;

        .cursor {
          layout: layout_YNIJ4Y;

          .cursor-image {
            fills: fill_BUP8OD;
            strokes: stroke_3ZBXST;
            layout: layout_QIXP2D;
          }
        }
      }

      .cursor-labels:nth-child(4) {
        fills: fill_PDX7L1;
        strokes: stroke_3ZBXST;
        layout: layout_QDF1R3;

        .cursor {
          layout: layout_SIBJ5Q;

          .cursor-image {
            fills: fill_PDX7L1;
            strokes: stroke_3ZBXST;
            layout: layout_C2DZSE;
          }
        }
      }

      .shadow {
        fills: fill_AIM7A4;
        layout: layout_UWY62R;
      }

      .bounding-headline {
        fills: fill_D71APB;
        strokes: stroke_3ZBXST;
        layout: layout_38SW5P;

        .corner {
          fills: fill_YOWY92;
          strokes: stroke_3ZBXST;
        }

        .headline-text {
          font-family: style_WSK9FC;
          color: fill_AIM7A4;
          layout: layout_8DOZY9;
        }
      }

      .cursor-labels-instance {
        fills: fill_8ES4PP;
        strokes: stroke_3ZBXST;
        layout: layout_X254BU;

        .cursor-instance {
          layout: layout_UFXPG8;

          .cursor-image-instance {
            fills: fill_8ES4PP;
            strokes: stroke_3ZBXST;
            layout: layout_7RWS0P;
          }
        }
      }
    }

    .about-this-file {
      fills: fill_YOWY92;
      layout: layout_SG6HUZ;

      .ellipse-71 {
        fills: fill_YOWY92;
        strokes: stroke_FG5WB2;
        layout: layout_NPNZ3V;
      }

      .ellipse-72 {
        fills: fill_YOWY92;
        strokes: stroke_FG5WB2;
        layout: layout_AQRYDQ;
      }

      .file-title {
        font-family: style_GGU03M;
        color: fill_AIM7A4;
        layout: layout_N0UNXG;
      }

      .file-description {
        font-family: style_KVXBB7;
        color: fill_AIM7A4;
        layout: layout_V56T4U;
      }
    }

    .whats-in-this {
      fills: fill_YOWY92;
      layout: layout_SG6HUZ;

      .instruction-text {
        font-family: style_KVXBB7;
        color: fill_AIM7A4;
        layout: layout_J0RFNH;
      }

      .image-1 {
        fills: fill_LD7IH9;
        effects: effect_25DFCE;
        layout: layout_U6YBW8;
        border-radius: 4px;
      }

      .image-2 {
        fills: fill_45T7WQ;
        effects: effect_25DFCE;
        layout: layout_WVU4UO;
        border-radius: 4px;
      }

      .image-3 {
        fills: fill_Q9DWVF;
        effects: effect_25DFCE;
        layout: layout_C1681N;
        border-radius: 4px;
      }

      .vector-133 {
        strokes: stroke_ZZN9X5;
        layout: layout_0KUOOA;
      }
    }

    .practice-designs {
      fills: fill_4QCIZV;
      layout: layout_SG6HUZ;

      .shadow {
        fills: fill_AIM7A4;
        layout: layout_BN2WO7;
      }

      .bounding-headline {
        fills: fill_YOWY92;
        strokes: stroke_3ZBXST;
        layout: layout_JV4VVD;

        .corner {
          fills: fill_YOWY92;
          strokes: stroke_3ZBXST;
        }

        .designs-title {
          font-family: style_QY9BOL;
          color: fill_AIM7A4;
          layout: layout_8DOZY9;
        }
      }

      .vector-group {
        layout: layout_8POV7Y;
        border-radius: 0;

        .ellipse-278 {
          strokes: stroke_1CNCX0;
          layout: layout_1ZI7QU;
        }

        .ellipse-277 {
          strokes: stroke_3ZBXST;
          layout: layout_1ZI7QU;
        }

        .ellipse-276 {
          fills: fill_YOWY92;
          strokes: stroke_3ZBXST;
          layout: layout_Z56WPN;
        }
      }

      .common-icon-pen {
        layout: layout_N7QUMS;

        .union {
          fills: fill_YOWY92;
          layout: layout_KI00OK;

          .ellipse-6 {
            fills: fill_VL0JKF;
            layout: layout_9P3CH9;
          }

          .rectangle-4 {
            fills: fill_VL0JKF;
            layout: layout_TE7RP6;
          }

          .vector-16 {
            fills: fill_VL0JKF;
            layout: layout_NJ0KHW;
          }
        }

        .union:nth-child(2) {
          fills: fill_AIM7A4;
          layout: layout_U7HRXX;

          .union {
            strokes: stroke_L4MH9V;
            layout: layout_XM5XVC;

            .ellipse-6 {
              fills: fill_VL0JKF;
              layout: layout_9P3CH9;
            }

            .rectangle-4 {
              fills: fill_VL0JKF;
              layout: layout_TE7RP6;
            }

            .vector-16 {
              fills: fill_VL0JKF;
              layout: layout_NJ0KHW;
            }
          }

          .vector-17 {
            strokes: stroke_CERMFK;
            layout: layout_B7VVA9;
          }

          .ellipse-7 {
            fills: fill_YOWY92;
            layout: layout_G6PLP0;
          }
        }
      }

      .cursor-labels-instance {
        fills: fill_XIXJIP;
        strokes: stroke_3ZBXST;
        layout: layout_5NFFGQ;

        .cursor-instance {
          layout: layout_UFXPG8;

          .cursor-image-instance {
            fills: fill_XIXJIP;
            strokes: stroke_3ZBXST;
            layout: layout_7RWS0P;
          }
        }
      }
    }

    .homepage {
      fills: fill_YOWY92;
      layout: layout_0C8JNY;

      .homepage-text {
        font-family: style_EBFOEN;
        color: fill_AIM7A4;
        layout: layout_MGYQ6A;
      }

      .button {
        fills: fill_TIT9F2;
        layout: layout_ZFI9SY;
        border-radius: 8px;

        .button-text {
          font-family: style_NX3M40;
          color: fill_YOWY92;
          layout: layout_G8U5LD;
        }
      }
    }
  }
}