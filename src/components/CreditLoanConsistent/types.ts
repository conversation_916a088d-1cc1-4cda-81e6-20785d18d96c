/**
 * @file CreditLoanConsistent 组件类型定义
 */

import { ReactNode } from 'react';

export interface CreditLoanConsistentProps {
  /** 组件类名 */
  className?: string;
  /** 子元素 */
  children?: ReactNode;
  /** 标题 */
  title?: string;
  /** 描述文本 */
  description?: string;
  /** 是否显示 */
  visible?: boolean;
  /** 点击事件 */
  onClick?: () => void;
}

export interface CreditLoanConsistentRef {
  /** 显示组件 */
  show: () => void;
  /** 隐藏组件 */
  hide: () => void;
}
