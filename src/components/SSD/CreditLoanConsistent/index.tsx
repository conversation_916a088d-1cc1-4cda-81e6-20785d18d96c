/**
 * @file 信贷一致性组件
 * <AUTHOR> Assistant
 */

import { ReactNode } from 'react';
import classNames from 'classnames';

import styles from './index.module.scss';

export interface CreditLoanConsistentProps {
  /** 组件类名 */
  className?: string;
  /** 子元素 */
  children?: ReactNode;
  /** 标题 */
  title?: string;
  /** 描述文本 */
  description?: string;
  /** 是否显示 */
  visible?: boolean;
  /** 点击事件 */
  onClick?: () => void;
}

export default function CreditLoanConsistent(props: CreditLoanConsistentProps) {
  const {
    className,
    children,
    title = '信贷一致性',
    description,
    visible = true,
    onClick,
  } = props;

  if (!visible) {
    return null;
  }

  return (
    <div
      className={classNames(styles.creditLoanConsistent, className)}
      onClick={onClick}
    >
      {title && (
        <div className={styles.title}>
          {title}
        </div>
      )}
      {description && (
        <div className={styles.description}>
          {description}
        </div>
      )}
      {children && (
        <div className={styles.content}>
          {children}
        </div>
      )}
    </div>
  );
}
