import React, { useRef, useState } from 'react';
import styles from './index.module.scss';

interface UploadFileProps {
  accept?: string; // 允许的文件类型
  onUpload?: (file: File, url?: string) => void; // 上传成功回调
  uploadUrl: string; // 上传接口
  buttonText?: string;
}

const UploadFile: React.FC<UploadFileProps> = ({
  accept = '*',
  onUpload,
  uploadUrl,
  buttonText = '上传文件',
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploading, setUploading] = useState(false);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    setUploading(true);

    // 构造 FormData
    const formData = new FormData();
    formData.append('file', file);

    try {
      // 这里用 fetch 作为示例，实际可根据后端接口调整
      const res = await fetch(uploadUrl, {
        method: 'POST',
        body: formData,
      });
      const data = await res.json();
      // 假设返回 { url: 'xxx' }
      onUpload?.(file, data.url);
    } catch (err) {
      onUpload?.(file, undefined);
      // 可以加错误提示
    } finally {
      setUploading(false);
      if (fileInputRef.current) fileInputRef.current.value = '';
    }
  };

  return (
    <div className={styles.uploadFile}>
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        className={styles.input}
        onChange={handleFileChange}
        disabled={uploading}
      />
      <button
        className={styles.button}
        onClick={() => fileInputRef.current?.click()}
        disabled={uploading}
      >
        {uploading ? '上传中...' : buttonText}
      </button>
    </div>
  );
};

export default UploadFile; 